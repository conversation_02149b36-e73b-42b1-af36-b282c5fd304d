"""
Tree-sitter based parser for multiple languages.
"""

import logging
import os
from typing import Dict, List, Any, Optional
import importlib.util

# Import tree-sitter
from tree_sitter import Language, Parser, Node

from .base_parser import BaseCodeParser

logger = logging.getLogger(__name__)

class TreeSitterParser(BaseCodeParser):
    """Tree-sitter based parser supporting multiple languages"""
    
    def __init__(self):
        # Check if language modules are available
        self.languages = {}
        self.parsers = {}
        
        # Try to load Python parser
        try:
            # Try to load Python parser directly
            import tree_sitter_python
            
            # Get the language function
            if hasattr(tree_sitter_python, 'language'):
                py_language = tree_sitter_python.language()
                python_parser = Parser()
                python_parser.set_language(Language(py_language))
                self.languages['python'] = Language(py_language)
                self.parsers['python'] = python_parser
                logger.info("Python parser loaded successfully")
            else:
                logger.warning("Python parser module found but language function not available")
        except (ImportError, AttributeError) as e:
            logger.warning(f"Failed to load Python parser: {str(e)}")
        
        # Log the available parsers
        if self.parsers:
            logger.info(f"Tree-sitter parsers loaded: {', '.join(self.parsers.keys())}")
        else:
            logger.warning("No Tree-sitter parsers were loaded successfully")
    
    def parse_file(self, file_path: str, language: str = None) -> Dict:
        """
        Parse a file using Tree-sitter
        
        Args:
            file_path: Path to the file
            language: Language to use for parsing (auto-detected if None)
            
        Returns:
            Dictionary with parsed data
        """
        if not language:
            language = self._detect_language(file_path)
        
        if language not in self.parsers:
            logger.warning(f"Unsupported language: {language}")
            return self._empty_result(file_path, language)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            parser = self.parsers[language]
            tree = parser.parse(bytes(source_code, 'utf8'))
            
            functions = self.extract_functions(tree.root_node, source_code, language)
            classes = self.extract_classes(tree.root_node, source_code, language)
            relationships = self.extract_relationships(tree.root_node, source_code, language)
            imports = self._extract_imports(tree.root_node, source_code, language)
            
            return {
                "file": {
                    "path": file_path,
                    "language": language,
                    "imports": imports
                },
                "functions": functions,
                "classes": classes,
                "relationships": relationships
            }
        except Exception as e:
            logger.error(f"Failed to parse {language} file {file_path}: {str(e)}")
            return self._empty_result(file_path, language, str(e))
    
    def extract_functions(self, root_node: Node, source_code: str, language: str) -> List[Dict]:
        """Extract function definitions"""
        functions = []
        
        if language == 'python':
            query_string = """
                (function_definition
                    name: (identifier) @func_name
                    parameters: (parameters) @params
                    body: (block) @body
                ) @function
            """
        else:
            return functions
        
        try:
            query = self.languages[language].query(query_string)
            captures = query.captures(root_node)
            
            for node, capture_name in captures:
                if capture_name == 'function':
                    func_info = self._extract_function_info(node, source_code, language)
                    if func_info:
                        functions.append(func_info)
        except Exception as e:
            logger.error(f"Error extracting functions: {str(e)}")
        
        return functions
    
    def extract_classes(self, root_node: Node, source_code: str, language: str) -> List[Dict]:
        """Extract class definitions"""
        classes = []
        
        if language == 'python':
            query_string = """
                (class_definition
                    name: (identifier) @class_name
                    body: (block) @body
                ) @class
            """
        else:
            return classes
        
        try:
            query = self.languages[language].query(query_string)
            captures = query.captures(root_node)
            
            for node, capture_name in captures:
                if capture_name == 'class':
                    class_info = self._extract_class_info(node, source_code, language)
                    if class_info:
                        classes.append(class_info)
        except Exception as e:
            logger.error(f"Error extracting classes: {str(e)}")
        
        return classes
    
    def extract_relationships(self, root_node: Node, source_code: str, language: str) -> List[Dict]:
        """Extract function calls and dependencies"""
        relationships = []
        
        if language == 'python':
            query_string = """
                (call
                    function: (identifier) @func_name
                ) @call
            """
        else:
            return relationships
        
        try:
            query = self.languages[language].query(query_string)
            captures = query.captures(root_node)
            
            for node, capture_name in captures:
                if capture_name == 'call':
                    call_info = self._extract_call_info(node, source_code)
                    if call_info:
                        relationships.append(call_info)
        except Exception as e:
            logger.error(f"Error extracting relationships: {str(e)}")
        
        return relationships
    
    def _detect_language(self, file_path: str) -> str:
        """Detect language from file extension"""
        ext = os.path.splitext(file_path)[1].lower()
        
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'tsx',
            '.jsx': 'javascript'
        }
        
        return language_map.get(ext, 'unknown')
    
    def _extract_function_info(self, node: Node, source_code: str, language: str) -> Optional[Dict]:
        """Extract detailed function information"""
        try:
            name_node = None
            params_node = None
            
            for child in node.children:
                if child.type == 'identifier' and not name_node:
                    name_node = child
                elif child.type in ['parameters', 'formal_parameters']:
                    params_node = child
            
            if not name_node:
                return None
            
            func_name = source_code[name_node.start_byte:name_node.end_byte]
            
            params = []
            if params_node:
                for param_child in params_node.children:
                    if param_child.type == 'identifier':
                        param_name = source_code[param_child.start_byte:param_child.end_byte]
                        params.append({"name": param_name, "type": None})
            
            return {
                "name": func_name,
                "params": params,
                "lineno": node.start_point[0] + 1,
                "end_lineno": node.end_point[0] + 1,
                "docstring": self._extract_docstring(node, source_code, language)
            }
        except Exception as e:
            logger.error(f"Error extracting function info: {str(e)}")
            return None
    
    def _extract_class_info(self, node: Node, source_code: str, language: str) -> Optional[Dict]:
        """Extract detailed class information"""
        try:
            name_node = None
            body_node = None
            
            for child in node.children:
                if child.type == 'identifier' and not name_node:
                    name_node = child
                elif child.type in ['block', 'class_body']:
                    body_node = child
            
            if not name_node:
                return None
            
            class_name = source_code[name_node.start_byte:name_node.end_byte]
            
            methods = []
            if body_node:
                for child in body_node.children:
                    if child.type in ['function_definition', 'function_declaration', 'method_definition']:
                        method_info = self._extract_function_info(child, source_code, language)
                        if method_info:
                            methods.append(method_info)
            
            return {
                "name": class_name,
                "methods": methods,
                "lineno": node.start_point[0] + 1,
                "end_lineno": node.end_point[0] + 1,
                "docstring": self._extract_docstring(node, source_code, language)
            }
        except Exception as e:
            logger.error(f"Error extracting class info: {str(e)}")
            return None
    
    def _extract_call_info(self, node: Node, source_code: str) -> Optional[Dict]:
        """Extract function call information"""
        try:
            func_node = None
            for child in node.children:
                if child.type == 'identifier':
                    func_node = child
                    break
            
            if not func_node:
                return None
            
            func_name = source_code[func_node.start_byte:func_node.end_byte]
            
            return {
                "callee": func_name,
                "lineno": node.start_point[0] + 1
            }
        except Exception as e:
            logger.error(f"Error extracting call info: {str(e)}")
            return None
    
    def _extract_imports(self, root_node: Node, source_code: str, language: str) -> List[str]:
        """Extract import statements"""
        imports = []
        
        try:
            if language == 'python':
                query_string = """
                    (import_statement
                        name: (dotted_name) @import_name
                    )
                    (import_from_statement
                        module_name: (dotted_name) @module_name
                    )
                """
            else:
                return imports
            
            query = self.languages[language].query(query_string)
            captures = query.captures(root_node)
            
            for node, capture_name in captures:
                import_text = source_code[node.start_byte:node.end_byte]
                if import_text not in imports:
                    imports.append(import_text)
        
        except Exception as e:
            logger.error(f"Error extracting imports: {str(e)}")
        
        return imports
    
    def _extract_docstring(self, node: Node, source_code: str, language: str) -> Optional[str]:
        """Extract docstring from function or class"""
        try:
            if language == 'python':
                # Look for string literal as first statement in body
                for child in node.children:
                    if child.type == 'block':
                        for stmt in child.children:
                            if stmt.type == 'expression_statement':
                                for expr_child in stmt.children:
                                    if expr_child.type == 'string':
                                        docstring = source_code[expr_child.start_byte:expr_child.end_byte]
                                        return docstring.strip('"\'')
            return None
        except Exception:
            return None
    
    def _empty_result(self, file_path: str, language: str, error: str = None) -> Dict:
        """Return empty result structure"""
        result = {
            "file": {
                "path": file_path,
                "language": language,
                "imports": []
            },
            "functions": [],
            "classes": [],
            "relationships": []
        }
        
        if error:
            result["file"]["error"] = error
        
        return result