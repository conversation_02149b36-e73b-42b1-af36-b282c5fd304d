"""
Repository scanner with integrated Tree-sitter parsing.
"""

import argparse
import json
import logging
import os
import sys
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional

import git

# Configure logging with line numbers
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(lineno)d - %(levelname)s -  %(message)s"
)
logger = logging.getLogger(__name__)

# Try to import BitbucketClient from MAS and config
try:
    from packages.corelanggraph.bitbucket import Bitbucket<PERSON>lient
    from ..config import Config
    HAS_BITBUCKET_CLIENT = True
except ImportError:
    HAS_BITBUCKET_CLIENT = False
    logger.warning("BitbucketClient not available, falling back to direct Git operations")

# Import the Tree-sitter parser
try:
    from ..parsers.tree_sitter_parser import TreeSitterParser
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    logger.warning("Tree-sitter parser not available, will only scan file structure")

class RepositoryScanner:
    """
    Repository scanner that outputs structure and parsed code to JSON.
    Can scan local repositories or clone from Bitbucket.
    """
    
    def __init__(self):
        self.ignored_dirs = {'.git', '__pycache__', 'node_modules', 'venv', '.venv', 'dist', 'build'}
        self.language_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'tsx',
            '.jsx': 'javascript',
            '.java': 'java',
            '.cs': 'csharp',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.c': 'c',
            '.cpp': 'cpp',
            '.h': 'c',
            '.hpp': 'cpp'
        }
        
        # Initialize parsers
        self.parser = None
        if HAS_TREE_SITTER:
            try:
                self.parser = TreeSitterParser()
                # Check if any parsers were successfully loaded
                if not self.parser.parsers:
                    logger.warning("Tree-sitter parser initialized but no language parsers were loaded")
                    self.parser = None
                else:
                    logger.info(f"Tree-sitter parser initialized successfully with languages: {', '.join(self.parser.parsers.keys())}")
            except Exception as e:
                logger.warning(f"Failed to initialize Tree-sitter parser: {str(e)}")
        
        # Initialize Bitbucket client if available
        self.bitbucket_client = None
        if HAS_BITBUCKET_CLIENT:
            try:
                self.bitbucket_client = BitbucketClient(
                    username=Config.BITBUCKET_USERNAME,
                    password=Config.BITBUCKET_PASSWORD
                )
                logger.info("BitbucketClient initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize BitbucketClient: {str(e)}")
    
    def scan_repository(self, repo_url: str, output_path: str, parse_code: bool = True) -> str:
        """
        Scan a repository and output the structure to a JSON file.
        
        Args:
            repo_url: URL of the repository or path to local repository
            output_path: Path to output JSON file
            parse_code: Whether to parse code content (default: True)
            
        Returns:
            Path to the output JSON file
        """
        logger.info(f"Scanning repository: {repo_url}")
        
        # Ensure output directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"Created output directory: {output_dir}")
        
        # Determine if this is a local path or remote URL
        is_local = os.path.exists(repo_url) and os.path.isdir(repo_url)
        
        if is_local:
            repo_path = repo_url
            if not os.path.exists(os.path.join(repo_path, '.git')):
                raise ValueError(f"Not a git repository: {repo_path}")
        else:
            # Clone repository to temporary directory
            repo_path = self._clone_repository(repo_url)
        
        try:
            # Scan repository structure
            structure = self._scan_directory(repo_path, parse_code=parse_code)
            
            # Add repository metadata
            repo = git.Repo(repo_path)
            repo_name = os.path.basename(os.path.abspath(repo_path))
            
            result = {
                "repo": {
                    "name": repo_name,
                    "url": repo_url,
                    "default_branch": repo.active_branch.name,
                    "current_commit": repo.head.commit.hexsha,
                    "remote_urls": [remote.url for remote in repo.remotes] if repo.remotes else []
                },
                "structure": structure,
                "parsing_enabled": parse_code and self.parser is not None
            }
            
            # Write to JSON file
            with open(output_path, 'w') as f:
                json.dump(result, f, indent=2)
            
            logger.info(f"Scan completed. Results written to {output_path}")
            return output_path
        finally:
            # Clean up temporary directory if we cloned the repository
            if not is_local:
                import shutil
                shutil.rmtree(repo_path, ignore_errors=True)
    
    def _clone_repository(self, repo_url: str) -> str:
        """
        Clone a repository to a temporary directory.
        Uses BitbucketClient if available, otherwise falls back to direct Git operations.
        
        Args:
            repo_url: URL of the repository
            
        Returns:
            Path to the cloned repository
        """
        temp_dir = tempfile.mkdtemp(prefix="repo_scan_")
        
        if self.bitbucket_client and repo_url.startswith(("https://bitbucket.org/", "*****************:")):
            # Extract workspace and repo name from URL
            if repo_url.startswith("https://"):
                parts = repo_url.split("/")
                workspace = parts[-2]
                repo_name = parts[-1].replace(".git", "")
            else:  # SSH URL
                parts = repo_url.split(":")
                workspace_repo = parts[1].replace(".git", "")
                workspace, repo_name = workspace_repo.split("/")
            
            logger.info(f"Cloning repository using BitbucketClient: {workspace}/{repo_name}")
            self.bitbucket_client.clone_repository(workspace, repo_name, temp_dir)
        else:
            logger.info(f"Cloning repository using Git: {repo_url}")
            git.Repo.clone_from(repo_url, temp_dir)
        
        return temp_dir
    
    def _scan_directory(self, directory: str, relative_path: str = "", parse_code: bool = True) -> Dict:
        """
        Recursively scan a directory and build a structure.
        
        Args:
            directory: Directory to scan
            relative_path: Relative path from the repository root
            parse_code: Whether to parse code files
            
        Returns:
            Dictionary with directory structure
        """
        result = {
            "name": os.path.basename(directory),
            "type": "directory",
            "path": relative_path,
            "children": []
        }
        
        for item in os.listdir(directory):
            # Skip ignored directories
            if item in self.ignored_dirs:
                continue
            
            full_path = os.path.join(directory, item)
            item_relative_path = os.path.join(relative_path, item) if relative_path else item
            
            if os.path.isdir(full_path):
                # Recursively scan subdirectory
                child = self._scan_directory(full_path, item_relative_path, parse_code)
                result["children"].append(child)
            else:
                # Process file
                file_info = self._process_file(full_path, item_relative_path, parse_code)
                result["children"].append(file_info)
        
        return result
    
    def _process_file(self, full_path: str, relative_path: str, parse_code: bool) -> Dict:
        """
        Process a single file, optionally parsing its content.
        
        Args:
            full_path: Full path to the file
            relative_path: Relative path from repository root
            parse_code: Whether to parse the file content
            
        Returns:
            Dictionary with file information
        """
        ext = os.path.splitext(relative_path)[1].lower()
        language = self.language_extensions.get(ext)
        
        file_info = {
            "name": os.path.basename(relative_path),
            "type": "file",
            "path": relative_path,
            "size": os.path.getsize(full_path),
            "language": language
        }
        
        # Parse code content if enabled and parser is available
        if parse_code and self.parser and language in self.parser.parsers:
            try:
                parsed_content = self.parser.parse_file(full_path, language)
                
                # Add parsed content to file info
                if parsed_content.get("functions"):
                    file_info["functions"] = parsed_content["functions"]
                if parsed_content.get("classes"):
                    file_info["classes"] = parsed_content["classes"]
                if parsed_content.get("relationships"):
                    file_info["relationships"] = parsed_content["relationships"]
                if parsed_content.get("file", {}).get("imports"):
                    file_info["imports"] = parsed_content["file"]["imports"]
                
                logger.debug(f"Parsed {language} file: {relative_path}")
            except Exception as e:
                logger.warning(f"Failed to parse {relative_path}: {str(e)}")
                file_info["parse_error"] = str(e)
        
        return file_info


def scan_repo_to_json(repo_url: str, output_path: Optional[str] = None, parse_code: bool = True) -> str:
    """
    Convenience function to scan a repository and output to JSON.
    
    Args:
        repo_url: URL of the repository or path to local repository
        output_path: Path to output JSON file (optional)
        parse_code: Whether to parse code content (default: True)
        
    Returns:
        Path to the output JSON file
    """
    scanner = RepositoryScanner()
    
    if output_path is None:
        # Generate output path in the outputs directory
        repo_name = repo_url.split("/")[-1].replace(".git", "")
        output_path = os.path.join("outputs", f"{repo_name}_structure.json")
    
    return scanner.scan_repository(repo_url, output_path, parse_code)


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Scan a repository and output the structure to a JSON file."
    )
    
    parser.add_argument(
        "repo_url",
        help="URL of the repository or path to local repository"
    )
    
    parser.add_argument(
        "-o", "--output",
        help="Path to output JSON file",
        default=None
    )
    
    parser.add_argument(
        "--no-parse",
        action="store_true",
        help="Skip code parsing, only scan file structure"
    )
    
    args = parser.parse_args()
    
    try:
        output_path = scan_repo_to_json(
            repo_url=args.repo_url,
            output_path=args.output,
            parse_code=not args.no_parse
        )
        
        logger.info(f"Repository structure written to {output_path}")
        return 0
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())