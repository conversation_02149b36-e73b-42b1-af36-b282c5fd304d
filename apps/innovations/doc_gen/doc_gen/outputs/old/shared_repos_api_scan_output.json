{"repo": {"name": "repo_scan_bgkerifs", "url": "https://<EMAIL>/cymulate/shared_repos.git", "default_branch": "master", "current_commit": "c6fe1acba56336ce7bb3f153524769fca0fb0409", "remote_urls": ["https://<EMAIL>/cymulate/shared_repos.git"]}, "structure": {"name": "repo_scan_bgkerifs", "type": "directory", "path": "", "children": [{"name": ".n<PERSON><PERSON><PERSON>", "type": "file", "path": ".n<PERSON><PERSON><PERSON>", "size": 206, "language": null}, {"name": "test", "type": "directory", "path": "test", "children": [{"name": "__mocks__", "type": "directory", "path": "test/__mocks__", "children": [{"name": "optionsDb.js", "type": "file", "path": "test/__mocks__/optionsDb.js", "size": 532, "language": "javascript"}, {"name": "cy-shared-infra.js", "type": "file", "path": "test/__mocks__/cy-shared-infra.js", "size": 921, "language": "javascript"}, {"name": "mocks.js", "type": "file", "path": "test/__mocks__/mocks.js", "size": 467, "language": "javascript"}, {"name": "redis.js", "type": "file", "path": "test/__mocks__/redis.js", "size": 78, "language": "javascript"}]}, {"name": "setup", "type": "directory", "path": "test/setup", "children": [{"name": "setupTestMocks.js", "type": "file", "path": "test/setup/setupTestMocks.js", "size": 2212, "language": "javascript"}, {"name": "teardownTestHooks.js", "type": "file", "path": "test/setup/teardownTestHooks.js", "size": 147, "language": "javascript"}, {"name": "memoryMonitor.js", "type": "file", "path": "test/setup/memoryMonitor.js", "size": 781, "language": "javascript"}, {"name": "setupEnvTestHooks.js", "type": "file", "path": "test/setup/setupEnvTestHooks.js", "size": 321, "language": "javascript"}, {"name": "setupTestHooks.js", "type": "file", "path": "test/setup/setupTestHooks.js", "size": 281, "language": "javascript"}]}, {"name": "db", "type": "directory", "path": "test/db", "children": [{"name": "db.utils.js", "type": "file", "path": "test/db/db.utils.js", "size": 79, "language": "javascript"}, {"name": "db.handler.js", "type": "file", "path": "test/db/db.handler.js", "size": 2783, "language": "javascript"}]}]}, {"name": "jest.config.js", "type": "file", "path": "jest.config.js", "size": 1807, "language": "javascript"}, {"name": "enums", "type": "directory", "path": "enums", "children": [{"name": "baseAttack.enum.js", "type": "file", "path": "enums/baseAttack.enum.js", "size": 3089, "language": "javascript"}, {"name": "dynamicDashboard.enum.js", "type": "file", "path": "enums/dynamicDashboard.enum.js", "size": 138, "language": "javascript"}, {"name": "hopper.enum.js", "type": "file", "path": "enums/hopper.enum.js", "size": 213, "language": "javascript"}, {"name": "user.enum.js", "type": "file", "path": "enums/user.enum.js", "size": 568, "language": "javascript"}, {"name": "index.js", "type": "file", "path": "enums/index.js", "size": 805, "language": "javascript"}, {"name": "attackStatus.enum.js", "type": "file", "path": "enums/attackStatus.enum.js", "size": 993, "language": "javascript"}, {"name": "client.enum.js", "type": "file", "path": "enums/client.enum.js", "size": 376, "language": "javascript"}, {"name": "user.enum.spec.js", "type": "file", "path": "enums/user.enum.spec.js", "size": 837, "language": "javascript"}, {"name": "generationStatus.enum.js", "type": "file", "path": "enums/generationStatus.enum.js", "size": 159, "language": "javascript"}]}, {"name": "index.js", "type": "file", "path": "index.js", "size": 2423, "language": "javascript"}, {"name": ".giti<PERSON>re", "type": "file", "path": ".giti<PERSON>re", "size": 368, "language": null}, {"name": "package.json", "type": "file", "path": "package.json", "size": 2472, "language": null}, {"name": "Jenkins<PERSON><PERSON>", "type": "file", "path": "Jenkins<PERSON><PERSON>", "size": 861, "language": null}, {"name": "lib", "type": "directory", "path": "lib", "children": [{"name": "dlp.lib.js", "type": "file", "path": "lib/dlp.lib.js", "size": 20924, "language": "javascript"}, {"name": "files.lib.js", "type": "file", "path": "lib/files.lib.js", "size": 4272, "language": "javascript"}, {"name": "formats.json", "type": "file", "path": "lib/formats.json", "size": 2191, "language": null}, {"name": "user.lib.js", "type": "file", "path": "lib/user.lib.js", "size": 40908, "language": "javascript"}, {"name": "dynamicDashboard.lib.js", "type": "file", "path": "lib/dynamicDashboard.lib.js", "size": 227, "language": "javascript"}, {"name": "edr.lib.js", "type": "file", "path": "lib/edr.lib.js", "size": 62111, "language": "javascript"}, {"name": "it.lib.spec.js", "type": "file", "path": "lib/it.lib.spec.js", "size": 44203, "language": "javascript"}, {"name": "sso.lib.js", "type": "file", "path": "lib/sso.lib.js", "size": 2414, "language": "javascript"}, {"name": "types", "type": "directory", "path": "lib/types", "children": [{"name": "general", "type": "directory", "path": "lib/types/general", "children": [{"name": "override.ts", "type": "file", "path": "lib/types/general/override.ts", "size": 74, "language": "typescript"}, {"name": "moduls.ts", "type": "file", "path": "lib/types/general/moduls.ts", "size": 247, "language": "typescript"}, {"name": "response.ts", "type": "file", "path": "lib/types/general/response.ts", "size": 432, "language": "typescript"}]}, {"name": "models", "type": "directory", "path": "lib/types/models", "children": [{"name": "browsingPayload.model.ts", "type": "file", "path": "lib/types/models/browsingPayload.model.ts", "size": 718, "language": "typescript"}, {"name": "edr", "type": "directory", "path": "lib/types/models/edr", "children": [{"name": "edrPayload.model.ts", "type": "file", "path": "lib/types/models/edr/edrPayload.model.ts", "size": 972, "language": "typescript"}, {"name": "scenario.model.ts", "type": "file", "path": "lib/types/models/edr/scenario.model.ts", "size": 221, "language": "typescript"}, {"name": "edrExecutionMethod.model.ts", "type": "file", "path": "lib/types/models/edr/edrExecutionMethod.model.ts", "size": 703, "language": "typescript"}, {"name": "edrFunction.model.ts", "type": "file", "path": "lib/types/models/edr/edrFunction.model.ts", "size": 299, "language": "typescript"}]}, {"name": "user.model.ts", "type": "file", "path": "lib/types/models/user.model.ts", "size": 4234, "language": "typescript"}, {"name": "agent.model.ts", "type": "file", "path": "lib/types/models/agent.model.ts", "size": 2066, "language": "typescript"}, {"name": "externalReference.ts", "type": "file", "path": "lib/types/models/externalReference.ts", "size": 139, "language": "typescript"}, {"name": "attack", "type": "directory", "path": "lib/types/models/attack", "children": [{"name": "mailAttack.model.ts", "type": "file", "path": "lib/types/models/attack/mailAttack.model.ts", "size": 2179, "language": "typescript"}, {"name": "aptAttack.model.ts", "type": "file", "path": "lib/types/models/attack/aptAttack.model.ts", "size": 1958, "language": "typescript"}, {"name": "attackPayload.model.ts", "type": "file", "path": "lib/types/models/attack/attackPayload.model.ts", "size": 277, "language": "typescript"}, {"name": "browsingAttack.model.ts", "type": "file", "path": "lib/types/models/attack/browsingAttack.model.ts", "size": 2471, "language": "typescript"}, {"name": "edrAttack.model.ts", "type": "file", "path": "lib/types/models/attack/edrAttack.model.ts", "size": 2664, "language": "typescript"}, {"name": "dlpAttack.model.ts", "type": "file", "path": "lib/types/models/attack/dlpAttack.model.ts", "size": 1824, "language": "typescript"}, {"name": "mailAttackPayload.model.ts", "type": "file", "path": "lib/types/models/attack/mailAttackPayload.model.ts", "size": 1290, "language": "typescript"}, {"name": "attack.model.ts", "type": "file", "path": "lib/types/models/attack/attack.model.ts", "size": 291, "language": "typescript"}, {"name": "immediateThreatsAttack.model.ts", "type": "file", "path": "lib/types/models/attack/immediateThreatsAttack.model.ts", "size": 2004, "language": "typescript"}, {"name": "hopperAttack.model.ts", "type": "file", "path": "lib/types/models/attack/hopperAttack.model.ts", "size": 1615, "language": "typescript"}, {"name": "aptAttackPayload.model.ts", "type": "file", "path": "lib/types/models/attack/aptAttackPayload.model.ts", "size": 244, "language": "typescript"}, {"name": "browsingAttackCounters.model.ts", "type": "file", "path": "lib/types/models/attack/browsingAttackCounters.model.ts", "size": 172, "language": "typescript"}, {"name": "mailAttackTemplate.model.ts", "type": "file", "path": "lib/types/models/attack/mailAttackTemplate.model.ts", "size": 463, "language": "typescript"}]}, {"name": "client.model.ts", "type": "file", "path": "lib/types/models/client.model.ts", "size": 1507, "language": "typescript"}, {"name": "redTeam", "type": "directory", "path": "lib/types/models/redTeam", "children": [{"name": "redTeamTemplate.model.ts", "type": "file", "path": "lib/types/models/redTeam/redTeamTemplate.model.ts", "size": 58, "language": "typescript"}]}, {"name": "base.model.ts", "type": "file", "path": "lib/types/models/base.model.ts", "size": 143, "language": "typescript"}, {"name": "payload.model.ts", "type": "file", "path": "lib/types/models/payload.model.ts", "size": 1219, "language": "typescript"}, {"name": "domain.model.ts", "type": "file", "path": "lib/types/models/domain.model.ts", "size": 134, "language": "typescript"}, {"name": "template", "type": "directory", "path": "lib/types/models/template", "children": [{"name": "template.model.ts", "type": "file", "path": "lib/types/models/template/template.model.ts", "size": 113, "language": "typescript"}, {"name": "browsingTemplate.model.ts", "type": "file", "path": "lib/types/models/template/browsingTemplate.model.ts", "size": 546, "language": "typescript"}]}, {"name": "agentActionsToBinaryKey.model.ts", "type": "file", "path": "lib/types/models/agentActionsToBinaryKey.model.ts", "size": 156, "language": "typescript"}, {"name": "mitre", "type": "directory", "path": "lib/types/models/mitre", "children": [{"name": "mitreTactic.model.ts", "type": "file", "path": "lib/types/models/mitre/mitreTactic.model.ts", "size": 582, "language": "typescript"}, {"name": "nistTechnique.model.ts", "type": "file", "path": "lib/types/models/mitre/nistTechnique.model.ts", "size": 337, "language": "typescript"}, {"name": "mitreSoftware.model.ts", "type": "file", "path": "lib/types/models/mitre/mitreSoftware.model.ts", "size": 496, "language": "typescript"}, {"name": "mitreMitigation.model.ts", "type": "file", "path": "lib/types/models/mitre/mitreMitigation.model.ts", "size": 445, "language": "typescript"}, {"name": "mitreTechnique.model.ts", "type": "file", "path": "lib/types/models/mitre/mitreTechnique.model.ts", "size": 1483, "language": "typescript"}, {"name": "mitreGroup.model.ts", "type": "file", "path": "lib/types/models/mitre/mitreGroup.model.ts", "size": 466, "language": "typescript"}]}, {"name": "environment.model.ts", "type": "file", "path": "lib/types/models/environment.model.ts", "size": 515, "language": "typescript"}, {"name": "option.model.ts", "type": "file", "path": "lib/types/models/option.model.ts", "size": 127, "language": "typescript"}, {"name": "agentUser.model.ts", "type": "file", "path": "lib/types/models/agentUser.model.ts", "size": 528, "language": "typescript"}, {"name": "finding.model.ts", "type": "file", "path": "lib/types/models/finding.model.ts", "size": 729, "language": "typescript"}, {"name": "db", "type": "directory", "path": "lib/types/models/db", "children": [{"name": "db.model.ts", "type": "file", "path": "lib/types/models/db/db.model.ts", "size": 49, "language": "typescript"}]}, {"name": "report", "type": "directory", "path": "lib/types/models/report", "children": [{"name": "dynamicReport", "type": "directory", "path": "lib/types/models/report/dynamicReport", "children": [{"name": "dynamicReportGenerated.model.ts", "type": "file", "path": "lib/types/models/report/dynamicReport/dynamicReportGenerated.model.ts", "size": 719, "language": "typescript"}]}]}, {"name": "index.ts", "type": "file", "path": "lib/types/models/index.ts", "size": 2598, "language": "typescript"}, {"name": "recon", "type": "directory", "path": "lib/types/models/recon", "children": [{"name": "reconAsset.model.ts", "type": "file", "path": "lib/types/models/recon/reconAsset.model.ts", "size": 926, "language": "typescript"}, {"name": "reconScan.model.ts", "type": "file", "path": "lib/types/models/recon/reconScan.model.ts", "size": 905, "language": "typescript"}, {"name": "reconFindingCy2.model.ts", "type": "file", "path": "lib/types/models/recon/reconFindingCy2.model.ts", "size": 916, "language": "typescript"}]}, {"name": "integrationCredentials.model.ts", "type": "file", "path": "lib/types/models/integrationCredentials.model.ts", "size": 850, "language": "typescript"}, {"name": "agentBinaryFiles.model.ts", "type": "file", "path": "lib/types/models/agentBinaryFiles.model.ts", "size": 433, "language": "typescript"}]}, {"name": "exceptions", "type": "directory", "path": "lib/types/exceptions", "children": [{"name": "cyUnauthorized.exception.d.ts", "type": "file", "path": "lib/types/exceptions/cyUnauthorized.exception.d.ts", "size": 186, "language": "typescript"}, {"name": "cyUnauthorized.exception.js", "type": "file", "path": "lib/types/exceptions/cyUnauthorized.exception.js", "size": 219, "language": "javascript"}, {"name": "cyForbidden.exception.d.ts", "type": "file", "path": "lib/types/exceptions/cyForbidden.exception.d.ts", "size": 180, "language": "typescript"}, {"name": "cyNotFound.exception.js", "type": "file", "path": "lib/types/exceptions/cyNotFound.exception.js", "size": 211, "language": "javascript"}, {"name": "cy.exception.d.ts", "type": "file", "path": "lib/types/exceptions/cy.exception.d.ts", "size": 176, "language": "typescript"}, {"name": "cyNotFound.exception.d.ts", "type": "file", "path": "lib/types/exceptions/cyNotFound.exception.d.ts", "size": 178, "language": "typescript"}, {"name": "cyForbidden.exception.js", "type": "file", "path": "lib/types/exceptions/cyForbidden.exception.js", "size": 213, "language": "javascript"}, {"name": "cyBadRequest.exception.js", "type": "file", "path": "lib/types/exceptions/cyBadRequest.exception.js", "size": 215, "language": "javascript"}, {"name": "cyServerError.exception.js", "type": "file", "path": "lib/types/exceptions/cyServerError.exception.js", "size": 217, "language": "javascript"}, {"name": "cyServerError.exception.d.ts", "type": "file", "path": "lib/types/exceptions/cyServerError.exception.d.ts", "size": 184, "language": "typescript"}, {"name": "cyBadRequest.exception.d.ts", "type": "file", "path": "lib/types/exceptions/cyBadRequest.exception.d.ts", "size": 182, "language": "typescript"}, {"name": "cy.exception.js", "type": "file", "path": "lib/types/exceptions/cy.exception.js", "size": 269, "language": "javascript"}]}]}, {"name": "utils.lib.js", "type": "file", "path": "lib/utils.lib.js", "size": 42126, "language": "javascript"}, {"name": "browsing.lib.js", "type": "file", "path": "lib/browsing.lib.js", "size": 18975, "language": "javascript"}, {"name": "agent.lib.spec.js", "type": "file", "path": "lib/agent.lib.spec.js", "size": 47385, "language": "javascript"}, {"name": "sendEmailByTemplate.lib.js", "type": "file", "path": "lib/sendEmailByTemplate.lib.js", "size": 15164, "language": "javascript"}, {"name": "extensions.lib", "type": "directory", "path": "lib/extensions.lib", "children": [{"name": "index.js", "type": "file", "path": "lib/extensions.lib/index.js", "size": 201, "language": "javascript"}, {"name": "array.extensions.js", "type": "file", "path": "lib/extensions.lib/array.extensions.js", "size": 1015, "language": "javascript"}]}, {"name": "total.lib.spec.js", "type": "file", "path": "lib/total.lib.spec.js", "size": 16311, "language": "javascript"}, {"name": "recon.lib.js", "type": "file", "path": "lib/recon.lib.js", "size": 1246, "language": "javascript"}, {"name": "edr.lib.spec.js", "type": "file", "path": "lib/edr.lib.spec.js", "size": 11627, "language": "javascript"}, {"name": "constants", "type": "directory", "path": "lib/constants", "children": [{"name": "sso.const.js", "type": "file", "path": "lib/constants/sso.const.js", "size": 159, "language": "javascript"}, {"name": "integrations.const.js", "type": "file", "path": "lib/constants/integrations.const.js", "size": 1318, "language": "javascript"}, {"name": "recon.const.js", "type": "file", "path": "lib/constants/recon.const.js", "size": 128, "language": "javascript"}, {"name": "subscriber.const.js", "type": "file", "path": "lib/constants/subscriber.const.js", "size": 69, "language": "javascript"}, {"name": "cyWrapper.const.js", "type": "file", "path": "lib/constants/cyWrapper.const.js", "size": 79, "language": "javascript"}, {"name": "hopper.const.js", "type": "file", "path": "lib/constants/hopper.const.js", "size": 1119, "language": "javascript"}, {"name": "index.js", "type": "file", "path": "lib/constants/index.js", "size": 1094, "language": "javascript"}, {"name": "baseAttack.const.js", "type": "file", "path": "lib/constants/baseAttack.const.js", "size": 1079, "language": "javascript"}, {"name": "agent.const.spec.js", "type": "file", "path": "lib/constants/agent.const.spec.js", "size": 2256, "language": "javascript"}, {"name": "agent.const.js", "type": "file", "path": "lib/constants/agent.const.js", "size": 1538, "language": "javascript"}, {"name": "user.const.js", "type": "file", "path": "lib/constants/user.const.js", "size": 1287, "language": "javascript"}, {"name": "user.const.spec.js", "type": "file", "path": "lib/constants/user.const.spec.js", "size": 2025, "language": "javascript"}, {"name": "risks.js", "type": "file", "path": "lib/constants/risks.js", "size": 506, "language": "javascript"}, {"name": "dynamicDashbaord.const.js", "type": "file", "path": "lib/constants/dynamicDashbaord.const.js", "size": 194, "language": "javascript"}, {"name": "client.const.js", "type": "file", "path": "lib/constants/client.const.js", "size": 727, "language": "javascript"}, {"name": "utils.const.js", "type": "file", "path": "lib/constants/utils.const.js", "size": 137, "language": "javascript"}, {"name": "tenant.const.js", "type": "file", "path": "lib/constants/tenant.const.js", "size": 38, "language": "javascript"}, {"name": "mail.const.js", "type": "file", "path": "lib/constants/mail.const.js", "size": 120, "language": "javascript"}]}, {"name": "waf.lib.spec.js", "type": "file", "path": "lib/waf.lib.spec.js", "size": 22197, "language": "javascript"}, {"name": "vrt.lib.js", "type": "file", "path": "lib/vrt.lib.js", "size": 1982, "language": "javascript"}, {"name": "browsing.lib.spec.js", "type": "file", "path": "lib/browsing.lib.spec.js", "size": 6282, "language": "javascript"}, {"name": "mail.lib.dataspec.js", "type": "file", "path": "lib/mail.lib.dataspec.js", "size": 4123, "language": "javascript"}, {"name": "hopperSettings.lib.js", "type": "file", "path": "lib/hopperSettings.lib.js", "size": 4327, "language": "javascript"}, {"name": "recon.utils.lib.js", "type": "file", "path": "lib/recon.utils.lib.js", "size": 109, "language": "javascript"}, {"name": "mitre.lib.spec.js", "type": "file", "path": "lib/mitre.lib.spec.js", "size": 10461, "language": "javascript"}, {"name": "hopper.lib.js", "type": "file", "path": "lib/hopper.lib.js", "size": 72726, "language": "javascript"}, {"name": "hopperSettings.lib.spec.js", "type": "file", "path": "lib/hopperSettings.lib.spec.js", "size": 20065, "language": "javascript"}, {"name": "waf.lib.js", "type": "file", "path": "lib/waf.lib.js", "size": 26621, "language": "javascript"}, {"name": "apt.lib.js", "type": "file", "path": "lib/apt.lib.js", "size": 1701, "language": "javascript"}, {"name": "integrations.lib.js", "type": "file", "path": "lib/integrations.lib.js", "size": 56864, "language": "javascript"}, {"name": "agent.profile.lib.js", "type": "file", "path": "lib/agent.profile.lib.js", "size": 638, "language": "javascript"}, {"name": "agent.lib.js", "type": "file", "path": "lib/agent.lib.js", "size": 24232, "language": "javascript"}, {"name": "findings.lib.js", "type": "file", "path": "lib/findings.lib.js", "size": 11037, "language": "javascript"}, {"name": "mail.lib.spec.js", "type": "file", "path": "lib/mail.lib.spec.js", "size": 13744, "language": "javascript"}, {"name": "validators", "type": "directory", "path": "lib/validators", "children": [{"name": "utils.validator.d.ts", "type": "file", "path": "lib/validators/utils.validator.d.ts", "size": 1848, "language": "typescript"}, {"name": "utils.validator.js", "type": "file", "path": "lib/validators/utils.validator.js", "size": 3523, "language": "javascript"}]}, {"name": "total.lib.js", "type": "file", "path": "lib/total.lib.js", "size": 7504, "language": "javascript"}, {"name": "mitre.lib.js", "type": "file", "path": "lib/mitre.lib.js", "size": 12310, "language": "javascript"}, {"name": "it.lib.js", "type": "file", "path": "lib/it.lib.js", "size": 38569, "language": "javascript"}, {"name": "errors", "type": "directory", "path": "lib/errors", "children": [{"name": "UploadItError.js", "type": "file", "path": "lib/errors/UploadItError.js", "size": 632, "language": "javascript"}, {"name": "CymulateFieldError.js", "type": "file", "path": "lib/errors/CymulateFieldError.js", "size": 559, "language": "javascript"}, {"name": "CymulateValidationErrorMultiple.js", "type": "file", "path": "lib/errors/CymulateValidationErrorMultiple.js", "size": 606, "language": "javascript"}, {"name": "CymulateValidationError.js", "type": "file", "path": "lib/errors/CymulateValidationError.js", "size": 611, "language": "javascript"}, {"name": "index.js", "type": "file", "path": "lib/errors/index.js", "size": 197, "language": "javascript"}, {"name": "RedTeamError.js", "type": "file", "path": "lib/errors/RedTeamError.js", "size": 126, "language": "javascript"}]}, {"name": "hopper.lib.spec.js", "type": "file", "path": "lib/hopper.lib.spec.js", "size": 14434, "language": "javascript"}, {"name": "user.lib.spec.js", "type": "file", "path": "lib/user.lib.spec.js", "size": 1704, "language": "javascript"}, {"name": "mail.lib.js", "type": "file", "path": "lib/mail.lib.js", "size": 43205, "language": "javascript"}, {"name": "environments.lib.js", "type": "file", "path": "lib/environments.lib.js", "size": 4087, "language": "javascript"}, {"name": "services", "type": "directory", "path": "lib/services", "children": [{"name": "integrations.service.js", "type": "file", "path": "lib/services/integrations.service.js", "size": 3170, "language": "javascript"}, {"name": "rrule.js", "type": "file", "path": "lib/services/rrule.js", "size": 3200, "language": "javascript"}]}, {"name": "redTeam.lib.js", "type": "file", "path": "lib/redTeam.lib.js", "size": 7748, "language": "javascript"}, {"name": "emailPreference.lib.js", "type": "file", "path": "lib/emailPreference.lib.js", "size": 1437, "language": "javascript"}, {"name": "phishing.lib.js", "type": "file", "path": "lib/phishing.lib.js", "size": 3088, "language": "javascript"}, {"name": "isoCountries.json", "type": "file", "path": "lib/isoCountries.json", "size": 5854, "language": null}, {"name": "browsing.lib.dataspec.js", "type": "file", "path": "lib/browsing.lib.dataspec.js", "size": 11006, "language": "javascript"}, {"name": "modules.lib.js", "type": "file", "path": "lib/modules.lib.js", "size": 1481, "language": "javascript"}]}, {"name": "helpers", "type": "directory", "path": "helpers", "children": [{"name": "object.helper.js", "type": "file", "path": "helpers/object.helper.js", "size": 1117, "language": "javascript"}, {"name": "userAuthMethodHelper.js", "type": "file", "path": "helpers/userAuthMethodHelper.js", "size": 917, "language": "javascript"}, {"name": "filesServerHelper.js", "type": "file", "path": "helpers/filesServerHelper.js", "size": 5043, "language": "javascript"}, {"name": "index.js", "type": "file", "path": "helpers/index.js", "size": 608, "language": "javascript"}, {"name": "cacher.js", "type": "file", "path": "helpers/cacher.js", "size": 1234, "language": "javascript"}, {"name": "envHelper.js", "type": "file", "path": "helpers/envHelper.js", "size": 462, "language": "javascript"}, {"name": "agentGetResources.js", "type": "file", "path": "helpers/agentGetResources.js", "size": 1873, "language": "javascript"}, {"name": "serverAuthHelper.js", "type": "file", "path": "helpers/serverAuthHelper.js", "size": 510, "language": "javascript"}, {"name": "generalUtils.js", "type": "file", "path": "helpers/generalUtils.js", "size": 130, "language": "javascript"}]}]}}