"""
API endpoints for the repository scanner.
"""

import os
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Optional
from logger import info, error

from ..scanner.repository_scanner import scan_repo_to_json

# Create router
router = APIRouter(prefix="/scan", tags=["scan"])

# Models
class ScanRequest(BaseModel):
    repo_url: str
    output_path: Optional[str] = None
    parse_code: bool = True

class ScanResponse(BaseModel):
    status: str
    message: str
    output_path: Optional[str] = None

# Background tasks
def process_scan_task(repo_url: str, output_path: Optional[str] = None, parse_code: bool = True) -> str:
    """
    Process repository scan in background
    
    Args:
        repo_url: URL of the repository or path to local repository
        output_path: Path to output JSON file
        parse_code: Whether to parse code content
        
    Returns:
        Path to the output JSON file
    """
    try:
        info(f"Starting scan task for repository: {repo_url}")
        
        # If output_path is not specified, use the outputs directory
        if not output_path:
            repo_name = repo_url.split("/")[-1].replace(".git", "")
            output_path = os.path.join("outputs", f"{repo_name}_structure.json")
        elif not output_path.startswith("outputs/"):
            # Ensure output path is in the outputs directory
            output_path = os.path.join("outputs", os.path.basename(output_path))
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        # Scan repository
        result_path = scan_repo_to_json(repo_url, output_path, parse_code)
        
        info(f"Scan task completed for repository: {repo_url}")
        return result_path
    except Exception as e:
        error(f"Error in scan task: {str(e)}")
        raise

# API endpoints
@router.post("", response_model=ScanResponse)
async def scan_repository(request: ScanRequest, background_tasks: BackgroundTasks):
    """
    Scan a repository and output the structure to a JSON file.
    
    Args:
        request: Scan request
        background_tasks: Background tasks
        
    Returns:
        Scan response
    """
    try:
        # Add scan task to background tasks
        output_path = request.output_path
        
        if not output_path:
            # Generate output path based on repo name
            repo_name = request.repo_url.split("/")[-1].replace(".git", "")
            output_path = os.path.join("outputs", f"{repo_name}_structure.json")
        elif not output_path.startswith("outputs/"):
            # Ensure output path is in the outputs directory
            output_path = os.path.join("outputs", os.path.basename(output_path))
        
        background_tasks.add_task(
            process_scan_task,
            request.repo_url,
            output_path,
            request.parse_code
        )
        
        return ScanResponse(
            status="success",
            message=f"Scan started for repository: {request.repo_url}",
            output_path=output_path
        )
    except Exception as e:
        error(f"Error starting scan: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))