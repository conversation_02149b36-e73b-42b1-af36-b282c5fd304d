"""
Main entry point for the Documentation Generator application.
"""

import asyncio
import os
import signal
import time
import traceback
import logging
import uvicorn
from doc_gen.events.app import kafka_manager
from logger import info, error

# Configure standard logging with line numbers for libraries that use it
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s"
)

# The app is now correctly exported from app.main
def handle_sigterm(signum, frame):
    """Handle SIGTERM signal by performing cleanup and exiting gracefully."""
    info("Received SIGTERM signal. Performing cleanup...")
    # Add any cleanup code here
    kafka_manager.stop_consuming()
    kafka_manager.close()
    os._exit(0)

# Register SIGTERM handler


async def start_kafka_consumer():
    # Run the blocking kafka consumer in a thread pool
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, kafka_manager.start_consuming)

async def start_http_consumer():
    config = uvicorn.Config("doc_gen.api.app:app", host="0.0.0.0",
                           port=int(os.getenv("PORT", 19000)), reload=True)
    server = uvicorn.Server(config)
    await server.serve()

async def main():
     await asyncio.gather(
                start_kafka_consumer(),
                start_http_consumer()
            )

if __name__ == "__main__":
    # Ensure outputs directory exists
    os.makedirs("outputs", exist_ok=True)
    info("Ensuring outputs directory exists")
    
    try:
        asyncio.run(main())
    
        signal.signal(signal.SIGTERM, handle_sigterm)
        signal.signal(signal.SIGINT, handle_sigterm)

    except Exception as e:
        error(f"Error: {e}")
        error(f"Error type: {type(e)}")
        error(f"Error traceback: {traceback.format_exc()}")
        error(f"Error message: {str(e)}")
        error(f"Error args: {e.args}")